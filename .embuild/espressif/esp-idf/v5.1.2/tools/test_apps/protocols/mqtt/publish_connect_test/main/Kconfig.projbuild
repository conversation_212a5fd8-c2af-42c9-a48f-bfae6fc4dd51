menu "Example Configuration"

    config EXAMPLE_BROKER_SSL_URI
        string "Broker SSL URL"
        default "mqtts://mqtt.eclipseprojects.io:8883"
        help
            URL of an mqtt broker for ssl transport

    config EXAMPLE_BROKER_TCP_URI
        string "Broker TCP URL"
        default "mqtt://mqtt.eclipseprojects.io:1883"
        help
            URL of an mqtt broker for tcp transport

    config EXAMPLE_BROKER_WS_URI
        string "Broker WS URL"
        default "ws://mqtt.eclipseprojects.io:80/mqtt"
        help
            URL of an mqtt broker for ws transport

    config EXAMPLE_BROKER_WSS_URI
        string "Broker WSS URL"
        default "wss://mqtt.eclipseprojects.io:443/mqtt"
        help
            URL of an mqtt broker for wss transport

    config EXAMPLE_PUBLISH_TOPIC
        string "publish topic"
        default "/topic/publish/esp2py"
        help
            topic to which esp32 client publishes

    config EXAMPLE_SUBSCRIBE_TOPIC
        string "subscribe topic"
        default "/topic/subscribe/py2esp"
        help
            topic to which esp32 client subsribes (and expects data)

    config EXAMPLE_BROKER_CERTIFICATE_OVERRIDE
        string "Broker certificate override"
        default ""
        help
            Please leave empty if broker certificate included from a textfile; otherwise fill in a base64 part of PEM
            format certificate

    config EXAMPLE_BROKER_CERTIFICATE_OVERRIDDEN
        bool
        default y if EXAMPLE_BROKER_CERTIFICATE_OVERRIDE != ""

    config EXAMPLE_CONNECT_CASE_NO_CERT
        # Note: All the below config values (EXAMPLE_CONNECT_CASE...) are hidden and
        # used to give symbolic names to test cases, which are then referenced from both
        # the embedded C code as well as the test counterpart in python
        int
        default 1

    config EXAMPLE_CONNECT_CASE_SERVER_CERT
        int
        default 2

    config EXAMPLE_CONNECT_CASE_MUTUAL_AUTH
        int
        default 3

    config EXAMPLE_CONNECT_CASE_INVALID_SERVER_CERT
        int
        default 4

    config EXAMPLE_CONNECT_CASE_SERVER_DER_CERT
        int
        default 5

    config EXAMPLE_CONNECT_CASE_MUTUAL_AUTH_KEY_PWD
        int
        default 6

    config EXAMPLE_CONNECT_CASE_MUTUAL_AUTH_BAD_CRT
        int
        default 7

    config EXAMPLE_CONNECT_CASE_NO_CERT_ALPN
        int
        default 8

endmenu
