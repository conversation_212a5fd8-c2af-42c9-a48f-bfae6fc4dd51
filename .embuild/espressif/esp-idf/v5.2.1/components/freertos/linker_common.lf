# Linker fragment file for common FreeRTOS files (i.e., both "port_common.c" and "esp_additions/..."
# Flash function placements are listed per source file, in the order that they appear in the source file.

[mapping:freertos_common]
archive: libfreertos.a
entries:
    # ------------------------------------------------------------------------------------------------------------------
    # esp_additions/private_include/freertos_tasks_c_additions.h
    # Placement Rules (FreeRTOS API Additions):
    #   - Default: Place all functions in internal RAM.
    #   - CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH: Place functions in flash if they are never called from an ISR
    #     context (directly or indirectly).
    # Placement Rules (Task Snapshot):
    #   - Default: Place all functions in internal RAM.
    #   - CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH: Place functions in flash
    #   - vTaskGetSnapshot is omitted on purpose as it is used to by the Task Watchdog (TWDT) interrupt handler, we want
    #     to always keep it in IRAM
    # ------------------------------------------------------------------------------------------------------------------
    if FREERTOS_PLACE_FUNCTIONS_INTO_FLASH = y:
        # Kernel Control
        if FREERTOS_SMP = n && FREERTOS_UNICORE = n:
            tasks:prvTakeKernelLock (default)
            tasks:prvReleaseKernelLock (default)
        # Task Creation
        tasks:xTaskCreatePinnedToCore (default)
        tasks:xTaskCreateStaticPinnedToCore (default)
        # Task Utilities
        tasks:xTaskGetCoreID (default)
        tasks:xTaskGetIdleTaskHandleForCore (default)
        tasks:xTaskGetCurrentTaskHandleForCore (default)
        if FREERTOS_SMP = n && FREERTOS_GENERATE_RUN_TIME_STATS = y:
            tasks:ulTaskGetIdleRunTimeCounterForCore (default)
            tasks:ulTaskGetIdleRunTimePercentForCore (default)
        tasks:pxTaskGetStackStart (default)
        tasks:prvTaskPriorityRaise (default)
        tasks:prvTaskPriorityRestore (default)
        # TLSP Deletion Callbacks
        if FREERTOS_TLSP_DELETION_CALLBACKS = y:
            tasks:vTaskSetThreadLocalStoragePointerAndDelCallback (default)
    # Task Snapshot
    if FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH = y:
        tasks:pxGetTaskListByIndex (default)
        tasks:xTaskGetNext (default)
        tasks:uxTaskGetSnapshotAll (default)

    # ------------------------------------------------------------------------------------------------------------------
    # freertos_compatibility.c
    # Placement Rules: Functions always in flash as they are never called from an ISR
    # ------------------------------------------------------------------------------------------------------------------
    freertos_compatibility (default)

    # ------------------------------------------------------------------------------------------------------------------
    # idf_additions.c
    # Placement Rules: Functions always in flash as they are never called from an ISR
    # ------------------------------------------------------------------------------------------------------------------
    idf_additions (default)

    # ------------------------------------------------------------------------------------------------------------------
    # app_startup.c
    # Placement Rules: Functions always in flash as they are never called from an ISR
    # ------------------------------------------------------------------------------------------------------------------
    app_startup (default)           # Place functions (but not Data and BSS) to flash

    # ------------------------------------------------------------------------------------------------------------------
    # heap_idf.c
    # Placement Rules:
    #   - Default: Place all functions in internal RAM.
    #   - CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH: Place functions in flash if they are never called from an ISR
    #     context (directly or indirectly).
    # ------------------------------------------------------------------------------------------------------------------
    heap_idf (noflash_text)         # Default all functions to internal RAM
    if FREERTOS_PLACE_FUNCTIONS_INTO_FLASH = y:
        heap_idf:pvPortMalloc (default)
        heap_idf:vPortFree (default)
        heap_idf:xPortGetFreeHeapSize (default)
        heap_idf:xPortGetMinimumEverFreeHeapSize (default)
        if FREERTOS_SMP = n:
            heap_idf:xPortCheckValidTCBMem (default)
            heap_idf:xPortcheckValidStackMem (default)

    # ------------------------------------------------------------------------------------------------------------------
    # port_common.c
    # Placement Rules:
    #   - Default: Place all functions in internal RAM.
    #   - CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH: Place functions in flash if they are never called from an ISR
    #     context (directly or indirectly).
    # ------------------------------------------------------------------------------------------------------------------
    port_common (noflash_text)      # Default all functions to internal RAM
    if FREERTOS_PLACE_FUNCTIONS_INTO_FLASH = y:
        port_common:vApplicationGetIdleTaskMemory (default)
        port_common:vApplicationGetTimerTaskMemory (default)

    # ------------------------------------------------------------------------------------------------------------------
    # port_systick.c
    #   - Default: Place all functions in internal RAM.
    #   - CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH: Place functions in flash if they are never called from an ISR
    #     context (directly or indirectly).
    # ------------------------------------------------------------------------------------------------------------------
    port_systick(noflash_text)
    if FREERTOS_PLACE_FUNCTIONS_INTO_FLASH = y:
        if FREERTOS_SYSTICK_USES_SYSTIMER = y:
            port_systick:vSystimerSetup (default)
        port_systick:vPortSetupTimer (default)
