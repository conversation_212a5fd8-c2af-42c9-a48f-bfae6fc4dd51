# Test configuration for enabling multiple optional FreeRTOS related features. Tested on all targets

CONFIG_FREERTOS_CORETIMER_1=y
CONFIG_FREERTOS_OPTIMIZED_SCHEDULER=n
CONFIG_FREERTOS_HZ=500
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL=y
CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK=y
CONFIG_FREERTOS_INTERRUPT_BACKTRACE=n
CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION=y
CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK=y
CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE=10
CONFIG_FREERTOS_USE_TRACE_FACILITY=y
CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS=y
CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID=y
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
CONFIG_FREERTOS_RUN_TIME_COUNTER_TYPE_U64=y
CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH=y
CONFIG_FREERTOS_FPU_IN_ISR=y
CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES=2
