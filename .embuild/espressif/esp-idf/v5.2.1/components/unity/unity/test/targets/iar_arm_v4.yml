tools_root: &tools_root 'C:\Program Files\IAR Systems\Embedded Workbench 4.0 Kickstart\'
compiler:
  path:             [*tools_root, 'arm\bin\iccarm.exe']
  source_path:      '..\src\'
  unit_tests_path:  &unit_tests_path 'tests\'
  build_path:       &build_path 'build\'
  options:
    - --dlib_config
    - [*tools_root, 'arm\lib\dl4tptinl8n.h']
    - -z3
    - --no_cse
    - --no_unroll
    - --no_inline
    - --no_code_motion
    - --no_tbaa
    - --no_clustering
    - --no_scheduling
    - --debug
    - --cpu_mode thumb
    - --endian little
    - --cpu ARM7TDMI
    - --stack_align 4
    - --interwork
    - -e
    - --silent
    - --warnings_are_errors
    - --fpu None
    - --diag_suppress Pa050
  includes:
    prefix: '-I'
    items:
      - [*tools_root, 'arm\inc\']
      - 'src\'
      - '..\src\'
      - 'testdata/'
      - *unit_tests_path
      - 'vendor\unity\src\'
  defines:
    prefix: '-D'
    items:
      - UNITY_SUPPORT_64
      - 'UNITY_SUPPORT_TEST_CASES'
  object_files:
    prefix: '-o'
    extension: '.r79'
    destination: *build_path
linker:
  path: [*tools_root, 'common\bin\xlink.exe']
  options:
    - -rt
    - [*tools_root, 'arm\lib\dl4tptinl8n.r79']
    - -D_L_EXTMEM_START=0
    - -D_L_EXTMEM_SIZE=0
    - -D_L_HEAP_SIZE=120
    - -D_L_STACK_SIZE=32
    - -e_small_write=_formatted_write
    - -s
    - __program_start
    - -f
    - [*tools_root, '\arm\config\lnkarm.xcl']
  includes:
    prefix: '-I'
    items:
      - [*tools_root, 'arm\config\']
      - [*tools_root, 'arm\lib\']
  object_files:
    path: *build_path
    extension: '.r79'
  bin_files:
    prefix: '-o'
    extension: '.d79'
    destination: *build_path
simulator:
  path: [*tools_root, 'common\bin\CSpyBat.exe']
  pre_support:
    - --silent
    - [*tools_root, 'arm\bin\armproc.dll']
    - [*tools_root, 'arm\bin\armsim.dll']
  post_support:
    - --plugin
    - [*tools_root, 'arm\bin\armbat.dll']
    - --backend
    - -B
    - -p
    - [*tools_root, 'arm\config\ioat91sam7X256.ddf']
    - -d
    - sim
colour: true
:unity:
  :plugins: []
