#Default tool path for IAR 5.4 on Windows XP 64bit
tools_root: &tools_root 'C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\'
compiler:
  path:             [*tools_root, 'arm\bin\iccarm.exe']
  source_path:      '..\src\'
  unit_tests_path:  &unit_tests_path 'tests\'
  build_path:       &build_path 'build\'
  options:
    - --diag_suppress=Pa050
    #- --diag_suppress=Pe111
    - --debug
    - --endian=little
    - --cpu=Cortex-M3
    - --no_path_in_file_macros
    - -e
    - --fpu=None
    - --dlib_config
    - [*tools_root, 'arm\inc\DLib_Config_Normal.h']
    #- --preinclude --preinclude C:\Vss\T2 Working\common\system.h
    - --interwork
    - --warnings_are_errors
#    - Ohz
    - -Oh
#    - --no_cse
#    - --no_unroll
#    - --no_inline
#    - --no_code_motion
#    - --no_tbaa
#    - --no_clustering
#    - --no_scheduling

  includes:
    prefix: '-I'
    items:
      - [*tools_root, 'arm\inc\']
      - 'src\'
      - '..\src\'
      - 'testdata/'
      - *unit_tests_path
      - 'vendor\unity\src\'
      - 'iar\iar_v5\incIAR\'
  defines:
    prefix: '-D'
    items:
      - ewarm
      - PART_LM3S9B92
      - TARGET_IS_TEMPEST_RB1
      - USE_ROM_DRIVERS
      - UART_BUFFERED
      - UNITY_SUPPORT_64
  object_files:
    prefix: '-o'
    extension: '.r79'
    destination: *build_path
linker:
  path: [*tools_root, 'arm\bin\ilinkarm.exe']
  options:
    - --redirect _Printf=_PrintfLarge
    - --redirect _Scanf=_ScanfSmall
    - --semihosting
    - --entry __iar_program_start
    - --config
    - [*tools_root, 'arm\config\generic.icf']
#    - ['C:\Temp\lm3s9b92.icf']
  object_files:
    path: *build_path
    extension: '.o'
  bin_files:
    prefix: '-o'
    extension: '.out'
    destination: *build_path
simulator:
  path: [*tools_root, 'common\bin\CSpyBat.exe']
  pre_support:
    #- --silent
    - [*tools_root, 'arm\bin\armproc.dll']
    - [*tools_root, 'arm\bin\armsim2.dll']
  post_support:
    - --plugin
    - [*tools_root, 'arm\bin\armbat.dll']
    - --backend
    - -B
    - --endian=little
    - --cpu=Cortex-M3
    - --fpu=None
    - -p
    - [*tools_root, 'arm\config\debugger\TexasInstruments\iolm3sxxxx.ddf']
    - --semihosting
    - --device=LM3SxBxx
    #- -d
    #- sim
colour: true
:unity:
  :plugins: []
