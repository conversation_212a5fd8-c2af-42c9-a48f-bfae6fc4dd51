[package]
name = "esp32-chat-bot"
version = "0.1.0"
edition = "2021"

[dependencies]
esp-idf-hal = "0.45"
esp-idf-sys = { version = "0.36", features = ["binstart"] }
esp-idf-svc = "0.51"
embedded-hal = "0.2"
embedded-svc = "0.28"
embedded-graphics = "0.8"
ssd1306 = "0.8"
heapless = "0.7"
log = "0.4"
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[dev-dependencies]
mockall = "0.11"
tokio = { version = "1.0", features = ["full"] }

[build-dependencies]
embuild = { version = "0.31", features = ["espidf"] }

[[bin]]
name = "esp32-chat-bot"
path = "src/main.rs"

[[bin]]
name = "esp32-chat-bot-sim"
path = "src/main_sim.rs"

[profile.release]
opt-level = "s"

[profile.dev]
debug = true
opt-level = "z"