{"rustc_fingerprint": 6634401913773945006, "outputs": {"9535490022162630799": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.dylib\nlib___.dylib\nlib___.a\nlib___.dylib\n/Users/<USER>/.rustup/toolchains/esp\noff\npacked\nunpacked\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"unwind\"\nproc_macro\nrelocation_model=\"pic\"\ntarget_abi=\"\"\ntarget_arch=\"aarch64\"\ntarget_endian=\"little\"\ntarget_env=\"\"\ntarget_family=\"unix\"\ntarget_feature=\"aes\"\ntarget_feature=\"crc\"\ntarget_feature=\"dit\"\ntarget_feature=\"dotprod\"\ntarget_feature=\"dpb\"\ntarget_feature=\"dpb2\"\ntarget_feature=\"fcma\"\ntarget_feature=\"fhm\"\ntarget_feature=\"flagm\"\ntarget_feature=\"flagm2\"\ntarget_feature=\"fp16\"\ntarget_feature=\"frintts\"\ntarget_feature=\"jsconv\"\ntarget_feature=\"lor\"\ntarget_feature=\"lse\"\ntarget_feature=\"lse2\"\ntarget_feature=\"neon\"\ntarget_feature=\"paca\"\ntarget_feature=\"pacg\"\ntarget_feature=\"pan\"\ntarget_feature=\"pmuv3\"\ntarget_feature=\"ras\"\ntarget_feature=\"rcpc\"\ntarget_feature=\"rcpc2\"\ntarget_feature=\"rdm\"\ntarget_feature=\"sb\"\ntarget_feature=\"sha2\"\ntarget_feature=\"sha3\"\ntarget_feature=\"ssbs\"\ntarget_feature=\"v8.1a\"\ntarget_feature=\"v8.2a\"\ntarget_feature=\"v8.3a\"\ntarget_feature=\"v8.4a\"\ntarget_feature=\"vh\"\ntarget_has_atomic\ntarget_has_atomic=\"128\"\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"128\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"128\"\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"macos\"\ntarget_pointer_width=\"64\"\ntarget_thread_local\ntarget_vendor=\"apple\"\nub_checks\nunix\n", "stderr": ""}, "7586885781525303475": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\n/Users/<USER>/.rustup/toolchains/esp\noff\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"abort\"\nproc_macro\nrelocation_model=\"static\"\ntarget_abi=\"\"\ntarget_arch=\"xtensa\"\ntarget_endian=\"little\"\ntarget_env=\"newlib\"\ntarget_family=\"unix\"\ntarget_feature=\"atomctl\"\ntarget_feature=\"bool\"\ntarget_feature=\"coprocessor\"\ntarget_feature=\"debug\"\ntarget_feature=\"dfpaccel\"\ntarget_feature=\"div32\"\ntarget_feature=\"exception\"\ntarget_feature=\"fp\"\ntarget_feature=\"highpriinterrupts\"\ntarget_feature=\"interrupt\"\ntarget_feature=\"loop\"\ntarget_feature=\"mac16\"\ntarget_feature=\"memctl\"\ntarget_feature=\"miscsr\"\ntarget_feature=\"mul32\"\ntarget_feature=\"mul32high\"\ntarget_feature=\"nsa\"\ntarget_feature=\"prid\"\ntarget_feature=\"regprotect\"\ntarget_feature=\"rvector\"\ntarget_feature=\"s32c1i\"\ntarget_feature=\"sext\"\ntarget_feature=\"threadptr\"\ntarget_feature=\"timerint\"\ntarget_feature=\"windowed\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"espidf\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"espressif\"\nub_checks\nunix\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `xtensa-esp32-espidf`\n\nwarning: dropping unsupported crate type `cdylib` for target `xtensa-esp32-espidf`\n\nwarning: dropping unsupported crate type `proc-macro` for target `xtensa-esp32-espidf`\n\nwarning: 3 warnings emitted\n\n"}, "10852584251390857955": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.88.0-nightly (2ab28d2e7 2025-06-24) (********)\nbinary: rustc\ncommit-hash: 2ab28d2e728c222edd27f881fd18de24fd88332c\ncommit-date: 2025-06-24\nhost: aarch64-apple-darwin\nrelease: 1.88.0-nightly\nLLVM version: 19.1.2\n", "stderr": ""}, "3581984541850499414": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\n/Users/<USER>/.rustup/toolchains/esp\noff\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"abort\"\nproc_macro\nrelocation_model=\"static\"\ntarget_abi=\"\"\ntarget_arch=\"xtensa\"\ntarget_endian=\"little\"\ntarget_env=\"newlib\"\ntarget_family=\"unix\"\ntarget_feature=\"atomctl\"\ntarget_feature=\"bool\"\ntarget_feature=\"coprocessor\"\ntarget_feature=\"debug\"\ntarget_feature=\"dfpaccel\"\ntarget_feature=\"div32\"\ntarget_feature=\"exception\"\ntarget_feature=\"fp\"\ntarget_feature=\"highpriinterrupts\"\ntarget_feature=\"interrupt\"\ntarget_feature=\"loop\"\ntarget_feature=\"mac16\"\ntarget_feature=\"memctl\"\ntarget_feature=\"miscsr\"\ntarget_feature=\"mul32\"\ntarget_feature=\"mul32high\"\ntarget_feature=\"nsa\"\ntarget_feature=\"prid\"\ntarget_feature=\"regprotect\"\ntarget_feature=\"rvector\"\ntarget_feature=\"s32c1i\"\ntarget_feature=\"sext\"\ntarget_feature=\"threadptr\"\ntarget_feature=\"timerint\"\ntarget_feature=\"windowed\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"espidf\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"espressif\"\nub_checks\nunix\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `xtensa-esp32-espidf`\n\nwarning: dropping unsupported crate type `cdylib` for target `xtensa-esp32-espidf`\n\nwarning: dropping unsupported crate type `proc-macro` for target `xtensa-esp32-espidf`\n\nwarning: 3 warnings emitted\n\n"}, "16454233947225369760": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.dylib\nlib___.dylib\nlib___.a\nlib___.dylib\n/Users/<USER>/.rustup/toolchains/esp\noff\npacked\nunpacked\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"unwind\"\nproc_macro\nrelocation_model=\"pic\"\ntarget_abi=\"\"\ntarget_arch=\"aarch64\"\ntarget_endian=\"little\"\ntarget_env=\"\"\ntarget_family=\"unix\"\ntarget_feature=\"aes\"\ntarget_feature=\"crc\"\ntarget_feature=\"dit\"\ntarget_feature=\"dotprod\"\ntarget_feature=\"dpb\"\ntarget_feature=\"dpb2\"\ntarget_feature=\"fcma\"\ntarget_feature=\"fhm\"\ntarget_feature=\"flagm\"\ntarget_feature=\"flagm2\"\ntarget_feature=\"fp16\"\ntarget_feature=\"frintts\"\ntarget_feature=\"jsconv\"\ntarget_feature=\"lor\"\ntarget_feature=\"lse\"\ntarget_feature=\"lse2\"\ntarget_feature=\"neon\"\ntarget_feature=\"paca\"\ntarget_feature=\"pacg\"\ntarget_feature=\"pan\"\ntarget_feature=\"pmuv3\"\ntarget_feature=\"ras\"\ntarget_feature=\"rcpc\"\ntarget_feature=\"rcpc2\"\ntarget_feature=\"rdm\"\ntarget_feature=\"sb\"\ntarget_feature=\"sha2\"\ntarget_feature=\"sha3\"\ntarget_feature=\"ssbs\"\ntarget_feature=\"v8.1a\"\ntarget_feature=\"v8.2a\"\ntarget_feature=\"v8.3a\"\ntarget_feature=\"v8.4a\"\ntarget_feature=\"vh\"\ntarget_has_atomic\ntarget_has_atomic=\"128\"\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"128\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"128\"\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"macos\"\ntarget_pointer_width=\"64\"\ntarget_thread_local\ntarget_vendor=\"apple\"\nub_checks\nunix\n", "stderr": ""}, "18028913704523393874": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.88.0-nightly (2ab28d2e7 2025-06-24) (********)\nbinary: rustc\ncommit-hash: 2ab28d2e728c222edd27f881fd18de24fd88332c\ncommit-date: 2025-06-24\nhost: aarch64-apple-darwin\nrelease: 1.88.0-nightly\nLLVM version: 19.1.2\n", "stderr": ""}, "5380229435151555508": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.dylib\nlib___.dylib\nlib___.a\nlib___.dylib\n/Users/<USER>/.rustup/toolchains/esp\noff\npacked\nunpacked\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"unwind\"\nproc_macro\nrelocation_model=\"pic\"\ntarget_abi=\"\"\ntarget_arch=\"aarch64\"\ntarget_endian=\"little\"\ntarget_env=\"\"\ntarget_family=\"unix\"\ntarget_feature=\"aes\"\ntarget_feature=\"crc\"\ntarget_feature=\"dit\"\ntarget_feature=\"dotprod\"\ntarget_feature=\"dpb\"\ntarget_feature=\"dpb2\"\ntarget_feature=\"fcma\"\ntarget_feature=\"fhm\"\ntarget_feature=\"flagm\"\ntarget_feature=\"flagm2\"\ntarget_feature=\"fp16\"\ntarget_feature=\"frintts\"\ntarget_feature=\"jsconv\"\ntarget_feature=\"lor\"\ntarget_feature=\"lse\"\ntarget_feature=\"lse2\"\ntarget_feature=\"neon\"\ntarget_feature=\"paca\"\ntarget_feature=\"pacg\"\ntarget_feature=\"pan\"\ntarget_feature=\"pmuv3\"\ntarget_feature=\"ras\"\ntarget_feature=\"rcpc\"\ntarget_feature=\"rcpc2\"\ntarget_feature=\"rdm\"\ntarget_feature=\"sb\"\ntarget_feature=\"sha2\"\ntarget_feature=\"sha3\"\ntarget_feature=\"ssbs\"\ntarget_feature=\"v8.1a\"\ntarget_feature=\"v8.2a\"\ntarget_feature=\"v8.3a\"\ntarget_feature=\"v8.4a\"\ntarget_feature=\"vh\"\ntarget_has_atomic\ntarget_has_atomic=\"128\"\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"128\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"128\"\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"macos\"\ntarget_pointer_width=\"64\"\ntarget_thread_local\ntarget_vendor=\"apple\"\nub_checks\nunix\n", "stderr": ""}, "17751342690275571502": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.88.0-nightly (2ab28d2e7 2025-06-24) (********)\nbinary: rustc\ncommit-hash: 2ab28d2e728c222edd27f881fd18de24fd88332c\ncommit-date: 2025-06-24\nhost: aarch64-apple-darwin\nrelease: 1.88.0-nightly\nLLVM version: 19.1.2\n", "stderr": ""}, "18205110017956586180": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\n/Users/<USER>/.rustup/toolchains/esp\noff\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"abort\"\nproc_macro\nrelocation_model=\"static\"\ntarget_abi=\"\"\ntarget_arch=\"xtensa\"\ntarget_endian=\"little\"\ntarget_env=\"newlib\"\ntarget_family=\"unix\"\ntarget_feature=\"atomctl\"\ntarget_feature=\"bool\"\ntarget_feature=\"coprocessor\"\ntarget_feature=\"debug\"\ntarget_feature=\"dfpaccel\"\ntarget_feature=\"div32\"\ntarget_feature=\"exception\"\ntarget_feature=\"fp\"\ntarget_feature=\"highpriinterrupts\"\ntarget_feature=\"interrupt\"\ntarget_feature=\"loop\"\ntarget_feature=\"mac16\"\ntarget_feature=\"memctl\"\ntarget_feature=\"miscsr\"\ntarget_feature=\"mul32\"\ntarget_feature=\"mul32high\"\ntarget_feature=\"nsa\"\ntarget_feature=\"prid\"\ntarget_feature=\"regprotect\"\ntarget_feature=\"rvector\"\ntarget_feature=\"s32c1i\"\ntarget_feature=\"sext\"\ntarget_feature=\"threadptr\"\ntarget_feature=\"timerint\"\ntarget_feature=\"windowed\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"espidf\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"espressif\"\nub_checks\nunix\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `xtensa-esp32-espidf`\n\nwarning: dropping unsupported crate type `cdylib` for target `xtensa-esp32-espidf`\n\nwarning: dropping unsupported crate type `proc-macro` for target `xtensa-esp32-espidf`\n\nwarning: 3 warnings emitted\n\n"}}, "successes": {}}