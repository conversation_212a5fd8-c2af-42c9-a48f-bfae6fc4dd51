{"rustc": 13229274219568542253, "features": "[\"clap\", \"default\", \"env_logger\", \"log\", \"logging\", \"runtime\", \"which\", \"which-rustfmt\"]", "declared_features": "[\"clap\", \"default\", \"env_logger\", \"log\", \"logging\", \"runtime\", \"static\", \"testing_only_docs\", \"testing_only_extra_assertions\", \"testing_only_libclang_5\", \"testing_only_libclang_9\", \"which\", \"which-rustfmt\"]", "target": 17883862002600103897, "profile": 5347358027863023418, "path": 10827234177596328754, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bindgen-abe2d004adef0387/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}