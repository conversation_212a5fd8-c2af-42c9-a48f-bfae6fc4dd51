{"rustc": 13229274219568542253, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9287053058537004099, "build_script_build", false, 7394164461651273190], [4885725550624711673, "build_script_build", false, 11185227481684395572]], "local": [{"RerunIfEnvChanged": {"var": "LLVM_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBCLANG_PATH", "val": "/Users/<USER>/.rustup/toolchains/esp/xtensa-esp32-elf-clang/esp-19.1.2_20250225/esp-clang/lib"}}, {"RerunIfEnvChanged": {"var": "LIBCLANG_STATIC_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "BINDGEN_EXTRA_CLANG_ARGS_aarch64_apple_darwin", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}