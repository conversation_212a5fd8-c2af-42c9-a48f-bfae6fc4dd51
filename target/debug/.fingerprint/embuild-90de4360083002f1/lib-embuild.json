{"rustc": 13229274219568542253, "features": "[\"default\"]", "declared_features": "[\"bindgen\", \"cargo_toml\", \"cmake\", \"default\", \"dep-cmake\", \"elf\", \"espidf\", \"git\", \"glob\", \"globwalk\", \"home\", \"kconfig\", \"manifest\", \"pio\", \"remove_dir_all\", \"serde\", \"serde_json\", \"strum\", \"tempfile\", \"toml\", \"ureq\", \"which\", \"xmas-elf\"]", "target": 7396908022763839336, "profile": 5347358027863023418, "path": 221003519304427148, "deps": [[8008191657135824715, "thiserror", false, 3773462523879887970], [8410525223747752176, "shlex", false, 7197191543856005478], [10435729446543529114, "bitflags", false, 12043388505940096538], [11207653606310558077, "anyhow", false, 9565010068509662408], [12988613902613872100, "filetime", false, 193290459301505298], [13066042571740262168, "log", false, 386639283527285796]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/embuild-90de4360083002f1/dep-lib-embuild", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}