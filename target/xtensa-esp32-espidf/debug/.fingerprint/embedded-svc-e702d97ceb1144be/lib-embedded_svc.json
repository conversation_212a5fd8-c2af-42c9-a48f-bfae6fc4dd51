{"rustc": 13229274219568542253, "features": "[\"alloc\", \"anyhow\", \"std\"]", "declared_features": "[\"alloc\", \"anyhow\", \"async-task\", \"default\", \"experimental\", \"futures\", \"isr-async-executor\", \"json_io\", \"json_io_core\", \"num_enum\", \"serde-json-core\", \"serde_json\", \"std\", \"strum\", \"strum_macros\", \"use_numenum\", \"use_serde\", \"use_strum\"]", "target": 12036006659942270665, "profile": 6631859958711676357, "path": 11427929555015716846, "deps": [[212084847430524879, "no_std_net", false, 12494953825073783310], [3778966308548958430, "std", true, 1609347772209165946], [5232917721369523709, "heapless", false, 4248582876520384572], [5283411439217056619, "core", true, 15638767977839266959], [6436979938661751600, "serde", false, 716525973108490593], [6491792180230419711, "panic_abort", true, 14557574587124798202], [6496076224047833312, "enumset", false, 11076150856633976901], [9492241421864410462, "compiler_builtins", true, 12598882586574175924], [11207653606310558077, "anyhow", false, 17837497286752898187], [12292871154985835666, "alloc", true, 13604995818848249107], [13066042571740262168, "log", false, 18194577200960217959], [16004514561570924261, "proc_macro", true, 10283714559297108747], [16203201182948489914, "embedded_io", false, 5426249704867038820], [16203338811130749637, "panic_unwind", true, 13543307098075468189]], "local": [{"CheckDepInfo": {"dep_info": "xtensa-esp32-espidf/debug/.fingerprint/embedded-svc-e702d97ceb1144be/dep-lib-embedded_svc", "checksum": false}}], "rustflags": [], "config": 13562761665107883759, "compile_kind": 17772458492455676904}